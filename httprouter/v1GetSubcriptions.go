package httprouter

import (
	"context"
	"fmt"
	"github.com/foxcorp-product/commerce-purchase/constants"
	"net/http"
	"strings"

	entitlement "github.com/foxcorp-product/commerce-entitlement/client"
	"github.com/foxcorp-product/entitlement-sdk/jwtdecoder"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"

	"github.com/foxcorp-product/commerce-purchase/clients/subscription"
	"github.com/foxcorp-product/commerce-purchase/models"
	"github.com/foxcorp-product/commerce-purchase/utils"
)

type V1Subscription struct {
	Uid           string                        `json:"uid" api:"uid"`
	AltUserID     string                        `json:"altUserID,omitempty" api:"altUserID"`
	Subscriptions []models.V1SubscriptionObject `json:"subscriptions" api:"subscriptions"`
}

func (h V1PurchaseHandler) V1GetSubscriptions(w http.ResponseWriter, r *http.Request) {
	var (
		err  error
		span stats.Span
		ctx  = r.Context()
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, "v1PurchaseHandler.V1GetSubscriptions")
	defer func() { span.FinishWithError(err) }()
	r = r.WithContext(ctx)
	rh := request.GetFromContext(r.Context())
	rl := request.GetFromContext(ctx).GetLoggingEntry()

	jwtClaims := rh.GetJWTClaims()
	if jwtClaims == nil {
		rl.Errorf("error calling rh.GetJWTClaims(): %v", err)
		err = utils.ErrFieldRequired(fmt.Sprintf("header.%s OR header.%s for claims", jwtdecoder.AccessTokenHeader, jwtdecoder.AuthorizationHeader))
		sendError(ctx, w, "v1PurchaseHandler.V1GetSubscriptions", err)
		return
	}

	uid := jwtClaims.Uid
	if strings.TrimSpace(uid) == "" {
		sendError(ctx, w, "V1PurchaseHandler.V1GetSubscriptions", utils.NewErrBadRequest(errMissingUid))
		return
	}

	ctx, _, span = utils.ContextWithLogFieldsAndSpanTags(ctx, map[string]interface{}{
		"uid": uid,
	})
	res, err := h.d.clients.entitlements.PostS2sEntitlements(ctx, entitlement.PostS2sEntitlementsBody{
		Uid:   uid,
		AppID: rh.GetAPIKey().Brand,
	})
	if err != nil {
		sendError(ctx, w, "v1PurchaseHandler.V1GetSubscriptions", err)
		return
	}

	nationV2Entitlements := utils.FilterEntitlementsByAppId(res.Entitlements, constants.NationAppId)

	sendMigrationRequestEvent(ctx, h, nationV2Entitlements, jwtClaims)

	subs := &V1Subscription{
		Uid:           uid,
		Subscriptions: utils.EntitlementsToSubscriptions(nationV2Entitlements),
	}

	isUseEvergent, err := h.d.GetUseEvergent(ctx)
	if err != nil {
		sendError(ctx, w, "v1PurchaseHandler.V1GetSubscriptions", err)
		return
	}

	hasActiveNationV2Entitlement := false
	var nationEnt *entitlement.V1EntitlementObject
	if res != nil && len(nationV2Entitlements) > 0 {
		for _, e := range nationV2Entitlements {
			if e.Active {
				hasActiveNationV2Entitlement = true
				nationEnt = &e
				break
			}
		}
	}

	if isUseEvergent && (!hasActiveNationV2Entitlement || h.shouldDoForwardToComm1(ctx, nationV2Entitlements)) {
		var code int
		code, subs, err = h.getSubscriptionFromCommV1(ctx, uid)
		if err != nil {
			sendError(ctx, w, "V1PurchaseHandler.V1GetSubscriptions.V1PostHasSubscription", utils.WrapHttpError(code, err))
			return
		}
	}

	err = h.setAltUIDToStripeUID(ctx, uid, subs)
	if err != nil {
		sendError(ctx, w, "V1PurchaseHandler.V1GetSubscriptions.setAltUIDToStripeUID", err)
		return
	}

	var status strings.Builder
	for i, s := range subs.Subscriptions {
		if i > 0 {
			status.WriteString("|")
		}
		status.WriteString(fmt.Sprintf("%s=%s ", s.AppServiceId, s.Status))
	}
	rl.Infof("V1GetSubscriptions - uid: %v, sub_status: %v, isUseEvergent: %v, com2_isActive: %v, shouldForwardToCom1: %v", uid, status.String(), isUseEvergent, hasActiveNationV2Entitlement, h.shouldDoForwardToComm1(ctx, res.Entitlements))

	//temporary measure logic begins
	d2cEntitlements := utils.FilterEntitlementsByAppId(res.Entitlements, "d2c")

	nationD2cExists := false
	for _, e := range d2cEntitlements {
		if e.Active && e.ProductFamily == "foxnation" {
			nationD2cExists = true
			break
		}
	}

	//nothing additional is required
	if nationD2cExists {
		sendJSONResponse(w, r, subs, http.StatusOK)
		return
	}

	//nation doesn't exist in v2 d2c. let's check if it exists in v1 or in v2 but as standalone
	var nationSub *models.V1SubscriptionObject
	nationV1Exists := false
	for _, s := range subs.Subscriptions {
		if s.Active && s.AppId == "foxnation" {
			nationV1Exists = true
			nationSub = &s
			break
		}
	}

	//means we have nation in v1 or v2 as standalone but not as part of d2c
	if hasActiveNationV2Entitlement && nationEnt != nil {
		h.d.clients.entitlements.PostEntitlement(ctx, entitlement.PostEntitlementBody{
			AppServiceId:  nationEnt.AppServiceId,
			Charged:       nationEnt.Charged,
			StartDate:     nationEnt.StartDate,
			EndDate:       nationEnt.ValidityTill,
			FreeTrial:     nationEnt.FreeTrial,
			PurchaseId:    nationEnt.PurchaseId,
			PaymentMethod: nationEnt.PaymentMethod,
		}, entitlement.PostEntitlementHeaderParams{
			XApiKey:      rh.GetAPIKey().APIKey,
			XAccessToken: fmt.Sprintf("Bearer %s", rh.GetJWTToken()),
		},
		)
	} else if nationV1Exists && nationSub != nil {
		h.d.clients.entitlements.PostEntitlement(ctx, entitlement.PostEntitlementBody{
			AppServiceId:  nationSub.AppServiceId, //TODO: will this plan contain the right product family?
			StartDate:     *nationSub.StartDate,
			EndDate:       nationSub.ValidityTill,
			FreeTrial:     nationSub.Status == "FreeTrial", //TODO: is this right?
			PurchaseId:    nationSub.PurchaseId,
			PaymentMethod: nationSub.Partner,
		}, entitlement.PostEntitlementHeaderParams{
			XApiKey:      rh.GetAPIKey().APIKey,
			XAccessToken: fmt.Sprintf("Bearer %s", rh.GetJWTToken()),
		},
		)
	}

	sendJSONResponse(w, r, subs, http.StatusOK)
}

func (h V1PurchaseHandler) shouldDoForwardToComm1(ctx context.Context, entitlements entitlement.V1EntitlementEntitlements) bool {
	if len(entitlements) == 0 {
		return true
	}

	return h.isStoreIsOffForComm2(ctx, entitlements[0].PaymentMethod)
}

func (h V1PurchaseHandler) getSubscriptionFromCommV1(ctx context.Context, uid string) (int, *V1Subscription, error) {
	var (
		err  error
		span stats.Span
		rh   = request.GetFromContext(ctx)
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, "v1PurchaseHandler.V1GetSubscriptions.getSubscriptionFromCommV1")
	defer func() { span.FinishWithError(err) }()

	subs := &V1Subscription{Uid: uid}
	subs.Subscriptions = make([]models.V1SubscriptionObject, 0)
	HeaderParams := subscription.HeaderParams{
		XApigwApiId:  &h.d.cfg.VPCKeyCommEKSDelta,
		APIKey:       rh.GetAPIKey().APIKey,
		XAccessToken: fmt.Sprintf("Bearer %s", rh.GetJWTToken()),
	}

	hasSubscriptionReq := subscription.HasSubscriptionRequest{}
	resCommV1, code, err := h.d.clients.subscription.HasSubscription(ctx, hasSubscriptionReq, HeaderParams)
	if err != nil {
		return code, nil, err
	}

	if resCommV1 != nil {
		subs.Subscriptions = utils.SubscriptionsCommV1ToCommV2(resCommV1.ServiceDetails)
	}
	return code, subs, nil
}

func (h V1PurchaseHandler) setAltUIDToStripeUID(ctx context.Context, userID string, subs *V1Subscription) error {
	var (
		err  error
		span stats.Span
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, "v1PurchaseHandler.V1GetSubscriptions.setAltUIDToStripeUID")
	defer func() { span.FinishWithError(err) }()

	stripeCustomerId, err := h.getStripeCustomerId(ctx, userID)
	if err != nil && !isHttpErrorNotFound(err) {
		return newErrFailedStatusFailedDependency(err)
	}

	subs.AltUserID = stripeCustomerId
	return nil
}
