package httprouter

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/foxcorp-product/commerce-purchase/models"
	"github.com/foxcorp-product/commerce-purchase/purchase"
	"github.com/foxcorp-product/commerce-purchase/utils"
	"github.com/foxcorp-product/entitlement-sdk/jwtdecoder"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
	"github.com/stripe/stripe-go/v81"
)

func TestV1PurchaseHandler_V1GetCardDetails(t *testing.T) {
	// Request with context
	req := httptest.NewRequest("GET", "/default-payment-method", nil)
	rh := request.New()
	rh.SetJWTClaims(&jwtdecoder.Claims{Uid: "user123"})
	ctx := request.NewContext(req.Context(), rh)
	req = req.WithContext(ctx)

	type args struct {
		c                                         Clients
		w                                         *httptest.ResponseRecorder
		r                                         *http.Request
		getStripeCustomerDefaultPaymentMethodFunc func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error)
		getStripeCustomerIdFunc                   func(ctx context.Context, purchaseSvc purchase.Purchase, uid string) (string, error)
	}
	defaultArgs := func() args {
		newArgs := new(args)
		newArgs.r = req
		newArgs.w = httptest.NewRecorder()
		spc := newStripeProxyMock()
		newArgs.c = Clients{
			stripeProxy: spc,
		}
		newArgs.getStripeCustomerIdFunc = func(ctx context.Context, purchaseSvc purchase.Purchase, uid string) (string, error) {
			return "stripeId", nil
		}
		return *newArgs
	}
	tests := []struct {
		name      string
		buildArgs func() args
		errMsg    string
		want      models.V1GetCustomerPaymentMethodResponse
	}{
		{
			name: "missing jwt/uid",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.r = httptest.NewRequest("GET", "/purchase/web/billing-history", nil)
				return newArgs
			},
			errMsg: "for claims",
		},
		{
			name: "Card details with 200 status",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{
						ID:   "pm_1234",
						Type: "card",
						Card: &stripe.PaymentMethodCard{
							Brand: "visa",
							Last4: "1234",
						}}, nil
				}
				return newArgs
			},
			want: models.V1GetCustomerPaymentMethodResponse{
				Id:        utils.ToPtr("pm_1234"),
				IsVermont: utils.ToPtr(false),
				Brand:     utils.ToPtr("visa"),
				Last4:     utils.ToPtr("1234"),
				Type:      utils.ToPtr("card"),
				Wallet:    utils.ToPtr(""),
				ZipCode:   utils.ToPtr(""),
				Name:      nil,
			},
		},
		{
			name: "Card details with 200 status from Vermont",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{
						ID: "pm_1234",
						BillingDetails: &stripe.PaymentMethodBillingDetails{
							Address: &stripe.Address{
								PostalCode: "05602",
							},
						},
						Type: "card",
						Card: &stripe.PaymentMethodCard{
							Brand: "visa",
							Last4: "1234",
						}}, nil
				}
				return newArgs
			},
			want: models.V1GetCustomerPaymentMethodResponse{
				Id:        utils.ToPtr("pm_1234"),
				IsVermont: utils.ToPtr(true),
				Brand:     utils.ToPtr("visa"),
				Last4:     utils.ToPtr("1234"),
				Type:      utils.ToPtr("card"),
				Wallet:    utils.ToPtr(""),
				ZipCode:   utils.ToPtr("05602"),
				Name:      utils.ToPtr(""),
			},
		},
		{
			name: "Card details with wallet 200 status",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{
						ID:             "pm_1234",
						Type:           "card",
						BillingDetails: &stripe.PaymentMethodBillingDetails{Address: &stripe.Address{PostalCode: "05602"}},
						Card: &stripe.PaymentMethodCard{
							Brand: "visa",
							Last4: "1234",
							Wallet: &stripe.PaymentMethodCardWallet{
								Type: "apple_pay",
							},
						}}, nil
				}
				return newArgs
			},
			want: models.V1GetCustomerPaymentMethodResponse{
				Id:        utils.ToPtr("pm_1234"),
				IsVermont: utils.ToPtr(true),
				Brand:     utils.ToPtr("visa"),
				Last4:     utils.ToPtr("1234"),
				Type:      utils.ToPtr("card"),
				Wallet:    utils.ToPtr("apple_pay"),
				ZipCode:   utils.ToPtr("05602"),
				Name:      utils.ToPtr(""),
			},
		},
		{
			name: "Card details with name in billing details",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{
						ID:             "pm_1234",
						Type:           "card",
						BillingDetails: &stripe.PaymentMethodBillingDetails{Name: "John Doe", Address: &stripe.Address{PostalCode: "05602"}},
						Card: &stripe.PaymentMethodCard{
							Brand: "visa",
							Last4: "1234",
							Wallet: &stripe.PaymentMethodCardWallet{
								Type: "apple_pay",
							},
						}}, nil
				}
				return newArgs
			},
			want: models.V1GetCustomerPaymentMethodResponse{
				Id:        utils.ToPtr("pm_1234"),
				IsVermont: utils.ToPtr(true),
				Brand:     utils.ToPtr("visa"),
				Last4:     utils.ToPtr("1234"),
				Type:      utils.ToPtr("card"),
				Wallet:    utils.ToPtr("apple_pay"),
				ZipCode:   utils.ToPtr("05602"),
				Name:      utils.ToPtr("John Doe"),
			},
		},
		{
			name: "Payment method type paypal",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{
						ID: "pm_1234",
						Metadata: map[string]string{
							"payment_method_type": "paypal",
						},
					}, nil
				}
				return newArgs
			},
			want: models.V1GetCustomerPaymentMethodResponse{
				Id:        utils.ToPtr("pm_1234"),
				IsVermont: utils.ToPtr(false),
				Type:      utils.ToPtr("paypal"),
				Wallet:    utils.ToPtr(""),
				ZipCode:   utils.ToPtr(""),
			},
		},
		{
			name: "Payment method type link",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{
						ID:   "pm_1234",
						Link: &stripe.PaymentMethodLink{},
					}, nil
				}
				return newArgs
			},
			want: models.V1GetCustomerPaymentMethodResponse{
				Id:        utils.ToPtr("pm_1234"),
				IsVermont: utils.ToPtr(false),
				Type:      utils.ToPtr("link"),
				Wallet:    utils.ToPtr(""),
				ZipCode:   utils.ToPtr(""),
			},
		},
		{
			name: "Payment method type amazon pay",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{
						ID:        "pm_1234",
						AmazonPay: &stripe.PaymentMethodAmazonPay{},
					}, nil
				}
				return newArgs
			},
			want: models.V1GetCustomerPaymentMethodResponse{
				Id:        utils.ToPtr("pm_1234"),
				IsVermont: utils.ToPtr(false),
				Type:      utils.ToPtr("amazon_pay"),
				Wallet:    utils.ToPtr(""),
				ZipCode:   utils.ToPtr(""),
			},
		},
		{
			name: "Zip code comes from customer info",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{
						ID:        "pm_1234",
						AmazonPay: &stripe.PaymentMethodAmazonPay{},
						Customer: &stripe.Customer{
							Address: &stripe.Address{
								PostalCode: "05602",
							},
						},
					}, nil
				}
				return newArgs
			},
			want: models.V1GetCustomerPaymentMethodResponse{
				Id:        utils.ToPtr("pm_1234"),
				IsVermont: utils.ToPtr(false),
				Type:      utils.ToPtr("amazon_pay"),
				Wallet:    utils.ToPtr(""),
				ZipCode:   utils.ToPtr("05602"),
			},
		},
		{
			name: "Error when card details not found",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return &stripe.PaymentMethod{}, nil
				}
				return newArgs
			},
			errMsg: "card details not found for customer",
		},
		{
			name: "Error from getStripeCustomerDefaultPaymentMethod",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerDefaultPaymentMethodFunc = func(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
					return nil, fmt.Errorf("random error")
				}
				return newArgs
			},
			errMsg: "random error",
		},
		{
			name: "Error from getStripeCustomerId",
			buildArgs: func() args {
				newArgs := defaultArgs()
				newArgs.getStripeCustomerIdFunc = func(ctx context.Context, purchaseSvc purchase.Purchase, uid string) (string, error) {
					return "", fmt.Errorf("random error")
				}
				return newArgs
			},
			errMsg: "random error",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stat, _ := stats.New(handlerGetCardDetailsName, "v1", stats.WithDevMode())
			l, _ := logger.New(
				logger.WithLogLevel("info"),
				logger.WithoutTimestamp(),
			)
			args := tt.buildArgs()
			h := V1PurchaseHandler{
				d: &V1PurchaseDependency{
					stat:    stat,
					log:     l,
					clients: args.c,
				},
			}
			getStripeCustomerDefaultPaymentMethodFunc = args.getStripeCustomerDefaultPaymentMethodFunc
			getStripeCustomerIdFunc = args.getStripeCustomerIdFunc

			h.V1GetCardDetails(args.w, args.r)
			result := args.w.Result()
			defer result.Body.Close()

			if tt.errMsg != "" {
				assert.Contains(t, args.w.Body.String(), tt.errMsg, "unexpected error message")
				return
			}
			res := models.V1GetCustomerPaymentMethodResponse{}
			err := json.Unmarshal(args.w.Body.Bytes(), &res)
			if err != nil {
				t.Error("could not unmarshal body: ", err)
			}
			assert.Equal(t, tt.want, res)
		})
	}
}
