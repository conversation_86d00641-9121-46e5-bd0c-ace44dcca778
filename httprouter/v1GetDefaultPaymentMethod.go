package httprouter

import (
	"context"
	"errors"
	"net/http"

	"github.com/stripe/stripe-go/v81"

	"github.com/foxcorp-product/commerce-purchase/models"
	"github.com/foxcorp-product/commerce-purchase/purchase"
	"github.com/foxcorp-product/commerce-purchase/utils"
	stripeProxy "github.com/foxcorp-product/commerce-stripeproxy/client"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"
)

const (
	handlerGetCardDetailsName = "v1PurchaseHandler.V1GetCardDetails"
)

var getStripeCustomerDefaultPaymentMethodFunc = getStripeCustomerDefaultPaymentMethod

func getStripeCustomerDefaultPaymentMethod(ctx context.Context, stripeClient StripeProxyClient, stripeCustomerId string) (*stripe.PaymentMethod, error) {
	res, err := stripeClient.RetrieveCustomerDefaultPaymentMethod(ctx, stripeProxy.V1RetrieveDefaultPaymentInput{
		ID: stripeCustomerId,
	})

	if err != nil {
		return nil, err
	}

	return res, nil
}

func (h V1PurchaseHandler) V1GetCardDetails(w http.ResponseWriter, r *http.Request) {
	var (
		err  error
		span stats.Span
		ctx  = r.Context()
		rl   = request.GetFromContext(ctx).GetLoggingEntry()
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, handlerGetCardDetailsName)
	defer func() { span.FinishWithError(err) }()

	r = r.WithContext(ctx)
	rh := request.GetFromContext(r.Context())

	foxUid, err := confirmJwtUId(rh)
	if err != nil {
		sendError(ctx, w, handlerGetCardDetailsName, utils.WrapHttpError(http.StatusUnprocessableEntity, err))
		return
	}

	rl = rl.WithField("foxUid", foxUid)
	span.SetTag("input.foxUid", foxUid)

	// Pull purchase record using foxUid(fox)
	stripeCustomerId, err := getStripeCustomerIdFunc(ctx, h.d.purchaseSvc, foxUid)
	if err != nil {
		httpCode := http.StatusUnprocessableEntity
		if errors.Is(err, purchase.ErrGetStripeCustomerIdPurchaseNotFoundForUser) || errors.Is(err, purchase.ErrGetStripeCustomerIdMissingCustomerIDForUser) {
			httpCode = http.StatusNotFound
		}
		rl.Error(handlerGetCardDetailsName, err)
		sendError(ctx, w, handlerGetCardDetailsName, utils.WrapHttpError(httpCode, err))
		return
	}

	rl = rl.WithField("stripeCustomerId", stripeCustomerId)
	span.SetTag("input.stripeCustomerId", stripeCustomerId)

	defaultPaymentMethod, err := getStripeCustomerDefaultPaymentMethodFunc(ctx, h.d.clients.stripeProxy, stripeCustomerId)
	if err != nil {
		httpCode := http.StatusUnprocessableEntity
		if errors.Is(err, stripeProxy.ErrRetrieveDefaultPaymentMethodNotFound) {
			httpCode = http.StatusNotFound
		}
		rl.Error(handlerGetCardDetailsName, err)
		sendError(ctx, w, handlerGetCardDetailsName, utils.WrapHttpError(httpCode, err))
		return
	}

	var isVermont bool
	if defaultPaymentMethod.BillingDetails != nil && defaultPaymentMethod.BillingDetails.Address != nil {
		isVermont = isValidVermontZipCode(defaultPaymentMethod.BillingDetails.Address.PostalCode)
	}

	var zipCode string
	if defaultPaymentMethod.BillingDetails != nil && defaultPaymentMethod.BillingDetails.Address != nil {
		zipCode = defaultPaymentMethod.BillingDetails.Address.PostalCode
	}

	if zipCode == "" && defaultPaymentMethod.Customer != nil && defaultPaymentMethod.Customer.Address != nil {
		zipCode = defaultPaymentMethod.Customer.Address.PostalCode
	}

	var paymentMethodType string

	res := &models.V1GetCustomerPaymentMethodResponse{
		Id:        utils.ToPtr(defaultPaymentMethod.ID),
		IsVermont: utils.ToPtr(isVermont),
		ZipCode:   utils.ToPtr(zipCode),
		Wallet:    utils.ToPtr(""),
	}

	if defaultPaymentMethod.BillingDetails != nil {
		res.Name = utils.ToPtr(defaultPaymentMethod.BillingDetails.Name)
	}

	if defaultPaymentMethod.AmazonPay != nil {
		paymentMethodType = string(stripe.PaymentMethodTypeAmazonPay)
	} else if defaultPaymentMethod.Link != nil {
		paymentMethodType = string(stripe.PaymentMethodTypeLink)
	} else if pm, found := defaultPaymentMethod.Metadata["payment_method_type"]; found && pm == string(stripe.PaymentMethodTypePaypal) {
		paymentMethodType = string(stripe.PaymentMethodTypePaypal)
	} else {
		paymentMethodType = string(defaultPaymentMethod.Type)
		if defaultPaymentMethod.Card == nil {
			err = errors.New("card details not found for customer")
			rl.Error(handlerGetCardDetailsName, err)
			sendError(ctx, w, handlerGetCardDetailsName, utils.WrapHttpError(http.StatusUnprocessableEntity, err))
			return
		}

		card := defaultPaymentMethod.Card
		res.Brand = utils.ToPtr(string(card.Brand))
		res.Last4 = utils.ToPtr(card.Last4)

		if card.Wallet != nil {
			res.Wallet = utils.ToPtr(string(card.Wallet.Type))
		}
	}

	res.Type = utils.ToPtr(paymentMethodType)
	sendJSONResponse(w, r, res, http.StatusOK)
}
